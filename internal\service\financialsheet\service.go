package financialsheet

import (
	"context"
	"fmt"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	_financialsheet "github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/league" // Added league service import
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Find(ctx context.Context, id string) (*financialsheet.Record, error)
	FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error)
	FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool, planning bool) (*financialsheet.Record, error)
	Update(ctx context.Context, record *financialsheet.Record) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error)
	FindCategory(ctx context.Context, id string) (*financialsheet.Category, error)
	FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error)
	FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error)
	UpdateCategory(ctx context.Context, category *financialsheet.Category) error
	DeleteCategory(ctx context.Context, id string, userID string) error

	// Transaction CRUD
	CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, isRecurring bool) (*financialsheet.Record, error)
	CreateRecurringTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, recurrenceMonths []int) (*financialsheet.Record, error)
	CreateDreamTransaction(ctx context.Context, userID string, dreamID string, amount monetary.Amount) error
	FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error)
	FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year, month int, planning bool) ([]*financialsheet.Transaction, error)
	UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error)
	DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error)

	NoTransactions(ctx context.Context, record *financialsheet.Record) error

	// League System Methods - These are now handled by the league service
	// ManageLeagueProgression(ctx context.Context, userID string) error
	// GetCurrentLeagueStatus(ctx context.Context, userID string) (*financialsheet.League, error)
	// StartNewSeason(ctx context.Context, userID string) error
	// GetLeagueRanking(ctx context.Context) (*financialsheet.LeagueRanking, error)

	// Utility
	Initialize(ctx context.Context, userID string, userName string) error
	CountUserCategories(ctx context.Context, userID string) (int, error)
}

type service struct {
	Repository           _financialsheet.Repository
	DreamboardRepository dreamboard.Repository
	LeagueService        league.Service // Added league service
	GamificationService  gamification.Service
}

func New(repository _financialsheet.Repository, dreamboardRepository dreamboard.Repository, leagueService league.Service, gamificationService gamification.Service) Service {
	return &service{
		Repository:           repository,
		DreamboardRepository: dreamboardRepository,
		LeagueService:        leagueService, // Initialize league service
		GamificationService:  gamificationService,
	}
}

// CRUD
func (s *service) Find(ctx context.Context, id string) (*financialsheet.Record, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}

	record, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	if record != nil && !record.ObjectID.IsZero() {
		record.ID = record.ObjectID.Hex()
	}
	return record, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	record, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	if record != nil && !record.ObjectID.IsZero() {
		record.ID = record.ObjectID.Hex()
	}

	// Restore all transaction IDs
	for year := range record.YearData {
		for month := range record.YearData[year] {
			for i := range record.YearData[year][month].Transactions {
				record.YearData[year][month].Transactions[i].ID = record.YearData[year][month].Transactions[i].ObjectID.Hex()
			}
		}
	}

	return record, nil
}

// FindByUserAndPeriod retrieves a user's financial record filtered by year and month
func (s *service) FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool, planning bool) (*financialsheet.Record, error) {
	record, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// If no period specified at all, return full record
	if year == 0 && month == 0 {
		return record, nil
	}

	// Handle year-only filtering
	if year > 0 && month == 0 {
		return s.filterByYear(ctx, record, year)
	}

	// Handle month-only filtering
	if year == 0 && month > 0 {
		return s.filterByMonth(ctx, record, month)
	}

	// Handle specific year and month filtering
	return s.filterByYearAndMonth(ctx, record, userID, year, month, flatten, planning)
}

// filterByYear handles filtering by year only
func (s *service) filterByYear(ctx context.Context, record *financialsheet.Record, year int) (*financialsheet.Record, error) {
	filteredRecord := s.createFilteredRecord(record)

	if yearData, exists := record.YearData[year]; exists {
		filteredRecord.YearData[year] = yearData
	}

	// Calculate totals for the year
	if yearData, ok := record.YearData[year]; ok {
		for _, monthData := range yearData {
			if err := s.calculateTotalsFromCategories(ctx, record.UserID, filteredRecord, monthData.Categories); err != nil {
				return nil, err
			}
		}
	}

	filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
	return filteredRecord, nil
}

// filterByMonth handles filtering by month only across all years
func (s *service) filterByMonth(ctx context.Context, record *financialsheet.Record, month int) (*financialsheet.Record, error) {
	filteredRecord := s.createFilteredRecord(record)
	monthStr := fmt.Sprintf("%02d", month)

	for year, yearData := range record.YearData {
		if monthData, exists := yearData[monthStr]; exists {
			filteredRecord.YearData[year] = make(financialsheet.YearData)
			filteredRecord.YearData[year][monthStr] = monthData

			// Calculate totals
			if err := s.calculateTotalsFromCategories(ctx, record.UserID, filteredRecord, monthData.Categories); err != nil {
				return nil, err
			}
		}
	}

	filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
	return filteredRecord, nil
}

// filterByYearAndMonth handles filtering by specific year and month
func (s *service) filterByYearAndMonth(ctx context.Context, record *financialsheet.Record, userID string, year, month int, flatten bool, planning bool) (*financialsheet.Record, error) {
	filteredRecord := s.createFilteredRecord(record)

	// Get user categories
	userCategories, err := s.FindAllCategoriesByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Initialize year and month structure
	filteredRecord.YearData[year] = make(financialsheet.YearData)
	monthStr := fmt.Sprintf("%02d", month)

	// Build month data with categories
	monthData, err := s.buildMonthData(ctx, record, userCategories, year, monthStr, filteredRecord)
	if err != nil {
		return nil, err
	}

	// Update category IDs and set structure
	s.updateCategoryIDs(monthData, userCategories)
	s.setRecordStructure(filteredRecord, monthData, year, monthStr, flatten)

	// Apply planning mode if enabled
	if planning {
		if err := s.applyPlanningMode(ctx, filteredRecord, userID, year, monthStr, flatten); err != nil {
			return nil, err
		}
	}

	filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
	return filteredRecord, nil
}

// createFilteredRecord creates a new filtered record with basic fields
func (s *service) createFilteredRecord(record *financialsheet.Record) *financialsheet.Record {
	return &financialsheet.Record{
		ID:       record.ID,
		ObjectID: record.ObjectID,
		UserID:   record.UserID,
		UserName: record.UserName,
		Points:   record.Points,
		YearData: make(map[int]financialsheet.YearData),
	}
}

// calculateTotalsFromCategories calculates totals from category cards
func (s *service) calculateTotalsFromCategories(ctx context.Context, userID string, record *financialsheet.Record, categories []financialsheet.CategoryCard) error {
	for _, card := range categories {
		category, err := s.FindCategoryByUserAndIdentifier(ctx, userID, financialsheet.CategoryIdentifier(card.Identifier))
		if err != nil {
			return err
		}
		switch category.Type {
		case financialsheet.CategoryTypeIncome:
			record.TotalIncome += card.Value
		case financialsheet.CategoryTypeCostsOfLiving:
			record.TotalCostsOfLiving += card.Value
		case financialsheet.CategoryTypeExpense:
			record.TotalExpenses += card.Value
		}
	}
	return nil
}

// buildMonthData builds month data with appropriate categories
func (s *service) buildMonthData(ctx context.Context, record *financialsheet.Record, userCategories []*financialsheet.Category, year int, monthStr string, filteredRecord *financialsheet.Record) (financialsheet.MonthData, error) {
	// monthData := financialsheet.MonthData{
	//     Categories: make([]financialsheet.CategoryCard, 0, len(userCategories)),
	// }

	if existingYearData, ok := record.YearData[year]; ok {
		if existingMonthData, ok := existingYearData[monthStr]; ok {
			return s.buildFromExistingData(ctx, existingMonthData, userCategories, filteredRecord)
		}
		return s.buildFromCategories(userCategories), nil
	}

	return s.buildFromCategories(userCategories), nil
}

// buildFromExistingData builds month data from existing data
func (s *service) buildFromExistingData(ctx context.Context, existingMonthData financialsheet.MonthData, userCategories []*financialsheet.Category, filteredRecord *financialsheet.Record) (financialsheet.MonthData, error) {
	monthData := financialsheet.MonthData{
		Categories: make([]financialsheet.CategoryCard, 0, len(userCategories)),
	}

	// Create map of existing category cards and calculate totals
	categoryCardsMap := make(map[string]financialsheet.CategoryCard)
	for _, card := range existingMonthData.Categories {
		categoryCardsMap[card.Identifier] = card
		if err := s.calculateTotalsFromCategories(ctx, filteredRecord.UserID, filteredRecord, []financialsheet.CategoryCard{card}); err != nil {
			return monthData, err
		}
	}

	// Build categories in predefined order
	allCategories := financialsheet.GetAllCategories()
	for _, cat := range allCategories {
		identifier := string(cat.Identifier)
		if card, exists := categoryCardsMap[identifier]; exists {
			card.Type = cat.Type
			card.Icon = cat.Icon
			card.Background = cat.Background
			monthData.Categories = append(monthData.Categories, card)
		} else {
			monthData.Categories = append(monthData.Categories, s.createCategoryCard(&cat))
		}
	}

	// Add user-created categories
	for _, cat := range userCategories {
		if cat.User == "" {
			continue
		}
		identifier := string(cat.Identifier)
		if card, exists := categoryCardsMap[identifier]; exists {
			card.Type = cat.Type
			card.Icon = cat.Icon
			card.Background = cat.Background
			monthData.Categories = append(monthData.Categories, card)
		} else {
			monthData.Categories = append(monthData.Categories, s.createCategoryCard(cat))
		}
	}

	monthData.Transactions = existingMonthData.Transactions
	return monthData, nil
}

// buildFromCategories builds month data from categories only (no existing data)
func (s *service) buildFromCategories(userCategories []*financialsheet.Category) financialsheet.MonthData {
	monthData := financialsheet.MonthData{
		Categories: make([]financialsheet.CategoryCard, 0, len(userCategories)),
	}

	// Add all predefined categories with zero values
	allCategories := financialsheet.GetAllCategories()
	for _, cat := range allCategories {
		monthData.Categories = append(monthData.Categories, s.createCategoryCard(&cat))
	}

	// Add user-created categories with zero values
	for _, cat := range userCategories {
		if cat.User == "" {
			continue
		}
		monthData.Categories = append(monthData.Categories, s.createCategoryCard(cat))
	}

	return monthData
}

// createCategoryCard creates a category card from a category
func (s *service) createCategoryCard(cat *financialsheet.Category) financialsheet.CategoryCard {
	return financialsheet.CategoryCard{
		ID:         cat.ID,
		Identifier: string(cat.Identifier),
		Type:       cat.Type,
		Name:       cat.Name,
		Icon:       cat.Icon,
		Background: cat.Background,
		Value:      0,
	}
}

// updateCategoryIDs updates category IDs in month data
func (s *service) updateCategoryIDs(monthData financialsheet.MonthData, userCategories []*financialsheet.Category) {
	for i := range monthData.Categories {
		if i < len(userCategories) {
			monthData.Categories[i].ID = userCategories[i].ID
		}
	}
}

// setRecordStructure sets the record structure based on flatten flag
func (s *service) setRecordStructure(filteredRecord *financialsheet.Record, monthData financialsheet.MonthData, year int, monthStr string, flatten bool) {
	if flatten {
		filteredRecord.Categories = monthData.Categories
		filteredRecord.YearData = nil
	} else {
		filteredRecord.YearData[year][monthStr] = monthData
	}
}

// applyPlanningMode applies planning mode adjustments
func (s *service) applyPlanningMode(ctx context.Context, filteredRecord *financialsheet.Record, userID string, year int, monthStr string, flatten bool) error {
	// Get the dreamboard
	dreamboard, err := s.DreamboardRepository.FindByUser(ctx, userID)
	if err != nil {
		return err
	}

	// Get categories reference
	var categories []financialsheet.CategoryCard
	if flatten {
		categories = filteredRecord.Categories
	} else {
		categories = filteredRecord.YearData[year][monthStr].Categories
	}

	// Update dream category value
	for i, card := range categories {
		if card.Identifier == string(financialsheet.CategoryIdentifierDreams) {
			categories[i].Value = dreamboard.MonthlyNeeded
			break
		}
	}

	// Update categories back to record
	if flatten {
		filteredRecord.Categories = categories
	} else {
		monthData := filteredRecord.YearData[year][monthStr]
		monthData.Categories = categories
		filteredRecord.YearData[year][monthStr] = monthData
	}

	// Recalculate totals after planning mode adjustment
	filteredRecord.TotalIncome = 0
	filteredRecord.TotalCostsOfLiving = 0
	filteredRecord.TotalExpenses = 0

	return s.calculateTotalsFromCategories(ctx, userID, filteredRecord, categories)
}

func (s *service) CountUserCategories(ctx context.Context, userID string) (int, error) {
	categories, err := s.Repository.FindAllCategories(ctx)
	if err != nil {
		return 0, err
	}

	count := 0
	for _, cat := range categories {
		if cat.User == userID {
			count++
		}
	}
	return count, nil
}

func (s *service) Update(ctx context.Context, record *financialsheet.Record) error {
	if record.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(record.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid financial record ID", errors.Validation, err)
		}
		record.ObjectID = objID
	}

	return s.Repository.Update(ctx, record)
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}
	return s.Repository.Delete(ctx, objID)
}

// Category CRUD
func (s *service) CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	if err := category.Identifier.Validate(); err != nil {
		return nil, errors.New(errors.Service, "invalid category identifier", errors.Validation, err)
	}
	// Fix Identifier
	numbersOfUserCategories, err := s.CountUserCategories(ctx, userID)
	if err != nil {
		return nil, err
	}
	identifierStr := fmt.Sprintf("%s_%d", category.Identifier, (numbersOfUserCategories + 1))
	// Set type icon
	if category.Type == financialsheet.CategoryTypeIncome {
		category.Icon = financialsheet.CategoryIconNewCategoryIncome
		category.Background = financialsheet.CategoryBackgroundIncome
	} else if category.Type == financialsheet.CategoryTypeExpense {
		category.Icon = financialsheet.CategoryIconNewCategoryExpense
		category.Background = financialsheet.CategoryBackgroundExpense
	}

	newCategory := &financialsheet.Category{
		User:       userID,
		Identifier: financialsheet.CategoryIdentifier(identifierStr),
		Type:       category.Type,
		Name:       category.Name,
		Icon:       category.Icon,
		Background: category.Background,
		MoneySource: []financialsheet.MoneySource{
			financialsheet.MoneySourceOpt1,
		},
		PaymentMethod: []financialsheet.PaymentMethod{
			financialsheet.PaymentMethodOpt1,
			financialsheet.PaymentMethodOpt2,
			financialsheet.PaymentMethodOpt3,
			financialsheet.PaymentMethodOpt4,
			financialsheet.PaymentMethodOpt5,
			financialsheet.PaymentMethodOther,
		},
	}

	categoryID, err := s.Repository.CreateCategory(ctx, newCategory)
	if err != nil {
		return nil, err
	}

	newCategory.ID = categoryID
	return newCategory, nil
}

func (s *service) FindCategory(ctx context.Context, id string) (*financialsheet.Category, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}

	return s.Repository.FindCategory(ctx, objID)
}

func (s *service) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	return s.Repository.FindAllCategories(ctx)
}

func (s *service) FindAllCategoriesByUser(ctx context.Context, userID string) ([]*financialsheet.Category, error) {
	categories, err := s.Repository.FindAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	userCategories := []*financialsheet.Category{}
	for _, category := range categories {
		if category.User == userID || category.User == "" {
			userCategories = append(userCategories, category)
		}
	}
	return userCategories, nil
}

func (s *service) FindCategoryByUserAndIdentifier(ctx context.Context, userID string, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	return s.Repository.FindCategoryByUserAndIdentifier(ctx, userID, identifier)
}

func (s *service) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	if err := category.Identifier.Validate(); err != nil {
		return errors.New(errors.Service, "invalid category identifier", errors.Validation, err)
	}

	objID, err := primitive.ObjectIDFromHex(category.ID)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}
	category.ObjectID = objID

	return s.Repository.UpdateCategory(ctx, category)
}

func (s *service) DeleteCategory(ctx context.Context, id string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}

	// First, find the category to validate ownership and type
	category, err := s.Repository.FindCategory(ctx, objID)
	if err != nil {
		return err
	}

	// Check if it's a system category (empty user field)
	if category.User == "" {
		return errors.New(errors.Service, "cannot delete system categories", errors.Forbidden, nil)
	}

	// Check if the category belongs to the requesting user
	if category.User != userID {
		return errors.New(errors.Service, "you can only delete your own categories", errors.Forbidden, nil)
	}

	// Check if the category is being used in any transactions
	// Pass 0 for year and month as they are not relevant for this specific check.
	// An empty string for categoryType means all types.
	transactions, err := s.Repository.FindAllTransactions(ctx, userID, "", 0, 0)
	if err != nil {
		return err
	}

	for _, transaction := range transactions {
		if string(transaction.Category) == string(category.Identifier) {
			return errors.New(errors.Service, "cannot delete category that is being used in transactions", errors.Conflict, nil)
		}
	}

	return s.Repository.DeleteCategory(ctx, objID)
}

// Transaction CRUD
func (s *service) CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, isRecurring bool) (*financialsheet.Record, error) {
	year := transaction.Date.Year()
	month := transaction.Date.Format("01")

	// Use UTC for consistent "day" calculation across timezones.
	// Or, if you store user-specific timezones, use that location.
	loc := time.UTC
	now := time.Now().In(loc)

	// Create now for testing
	// now := time.Date(year, time.Month(transaction.Date.Month()), transaction.Date.Day(), 0, 0, 0, 0, loc)

	if record.Points.LastTransactionDate.IsZero() {
		// First-ever transaction for this user
		record.Points.Current = 1
		record.Points.Best = 1
	} else {
		// Truncate times to the beginning of the day (00:00:00) for accurate date comparison
		lastTxDay := time.Date(
			record.Points.LastTransactionDate.Year(),
			record.Points.LastTransactionDate.Month(),
			record.Points.LastTransactionDate.Day(),
			0, 0, 0, 0, loc)

		today := time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			0, 0, 0, 0, loc)

		// Calculate the difference in calendar days
		daysSinceLastTransaction := int(today.Sub(lastTxDay).Hours() / 24)

		if daysSinceLastTransaction == 0 {
			// Transaction on the same day. Do nothing to the streak.
			// The LastTransactionDate will be updated later.
		} else if daysSinceLastTransaction == 1 {
			// Consecutive day transaction: increment the streak
			record.Points.Current++
			if record.Points.Current > record.Points.Best {
				record.Points.Best = record.Points.Current
			}
		} else if daysSinceLastTransaction > 1 {
			// Streak is broken: reset to 1 for the current day's transaction
			record.Points.Current = 1
			record.Points.MissedDays = append(record.Points.MissedDays, record.Points.LastTransactionDate.AddDate(0, 0, 1))

			// EVALUATE LATER
			// Optional: More robust missed days calculation
			// This loop adds all days that were missed.
			// Note: This could create a large slice if the user was away for a long time.
			// You may want to cap it or stick with your simpler logic if it's sufficient.
			// missedDay := lastTxDay.AddDate(0, 0, 1)
			// for missedDay.Before(today) {
			// 	record.Points.MissedDays = append(record.Points.MissedDays, missedDay)
			// 	missedDay = missedDay.AddDate(0, 0, 1)
			// }
		}
	}
	// Always update the last transaction date to the current time
	record.Points.LastTransactionDate = now

	// Update league tier based on new streak - OLD LOGIC REMOVED
	// switch {
	// case record.Points.Current >= 90:
	// record.League.CurrentLevel = financialsheet.DiamondLevel
	// case record.Points.Current >= 60:
	// record.League.CurrentLevel = financialsheet.GoldLevel
	// case record.Points.Current >= 30:
	// record.League.CurrentLevel = financialsheet.SilverLevel
	// default:
	// record.League.CurrentLevel = financialsheet.BronzeLevel
	// }

	// Initialize year and month data if they don't exist
	if record.YearData == nil {
		record.YearData = make(map[int]financialsheet.YearData)
	}
	if _, ok := record.YearData[year]; !ok {
		record.YearData[year] = make(financialsheet.YearData)
	}

	// Initialize month data if it doesn't exist
	if _, ok := record.YearData[year][month]; !ok {
		record.YearData[year][month] = financialsheet.MonthData{
			Categories: make([]financialsheet.CategoryCard, 0),
		}
	}

	// Get category first to validate and use its information
	category, err := s.FindCategoryByUserAndIdentifier(ctx, record.UserID, transaction.Category)
	if err != nil {
		return nil, err
	}

	// Validate transaction type matches category type
	if category.Type != transaction.Type {
		return nil, errors.New(errors.Service, "transaction type does not match category type", errors.Validation, nil)
	}

	// Generate unique transaction ID
	transaction.ObjectID = primitive.NewObjectID()
	transaction.ID = transaction.ObjectID.Hex()

	// Get the current month's data and prepare updates
	monthData := record.YearData[year][month]

	// Update category summary
	var categoryFound bool
	var updatedCategories []financialsheet.CategoryCard

	// Update existing category summary or create new one
	for _, card := range monthData.Categories {
		newCard := card
		if card.Identifier == string(category.Identifier) {
			newCard.Value += transaction.Value
			categoryFound = true
		}
		updatedCategories = append(updatedCategories, newCard)
	}

	// Add new category summary if not found
	if !categoryFound {
		updatedCategories = append(updatedCategories, financialsheet.CategoryCard{
			ID:         category.ID,
			Identifier: string(category.Identifier),
			Name:       category.Name,
			Value:      transaction.Value,
		})
	}

	// Create new month data with updated categories and new transaction
	newMonthData := financialsheet.MonthData{
		Categories:   updatedCategories,
		Transactions: append(monthData.Transactions, *transaction),
	}

	// Update the month data in the record
	record.YearData[year][month] = newMonthData

	// Recalculate totals from all transactions to ensure consistency
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByUserAndIdentifier(ctx, record.UserID, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in the database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	// After successful financial record update, record investida for leagues
	if s.LeagueService != nil {
		if errLs := s.LeagueService.RecordTransactionForAllUserLeagues(ctx, record.UserID, transaction.Date); errLs != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to record investida for user %s in leagues: %v\n", record.UserID, errLs) // Placeholder for proper logging
		}
	}

	// Check for Explorer achievement
	if s.GamificationService != nil && !isRecurring {
		if err := s.GamificationService.CheckExplorerAchievement(ctx, record.UserID); err != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to check explorer achievement for user %s: %v\n", record.UserID, err) // Placeholder for proper logging
		}
	}

	// Check for Planning achievement
	if s.GamificationService != nil && !isRecurring {
		if err := s.GamificationService.CheckPlanningAchievement(ctx, record.UserID); err != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to check planning achievement for user %s: %v\n", record.UserID, err) // Placeholder for proper logging
		}
	}

	return record, nil
}

// CreateRecurringTransaction creates a transaction and its recurring instances
func (s *service) CreateRecurringTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction, recurrenceMonths []int) (*financialsheet.Record, error) {
	if record == nil {
		return nil, errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	if transaction == nil {
		return nil, errors.New(errors.Service, "invalid transaction", errors.BadRequest, nil)
	}

	// Validate recurrence months
	if err := s.validateRecurrenceMonths(recurrenceMonths, transaction.Date); err != nil {
		return nil, err
	}

	// Create the original transaction first
	updatedRecord, err := s.CreateTransaction(ctx, record, transaction, true)
	if err != nil {
		return nil, err
	}

	// Create recurring transactions for each month
	for _, month := range recurrenceMonths {
		// Calculate the target date for this recurrence
		targetDate := s.calculateRecurrenceDate(transaction.Date, month)

		// Create a copy of the transaction with the new date
		recurringTransaction := *transaction
		recurringTransaction.Date = targetDate
		recurringTransaction.ObjectID = primitive.ObjectID{} // Reset to generate new ID

		// Create the recurring transaction
		updatedRecord, err = s.CreateTransaction(ctx, updatedRecord, &recurringTransaction, true)
		if err != nil {
			return nil, err
		}
	}

	// Check for Planning achievement
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckPlanningAchievement(ctx, updatedRecord.UserID); err != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to check planning achievement for user %s: %v\n", updatedRecord.UserID, err) // Placeholder for proper logging
		}
	}

	return updatedRecord, nil
}

// validateRecurrenceMonths validates the recurrence months array
func (s *service) validateRecurrenceMonths(recurrenceMonths []int, originalDate time.Time) error {
	if len(recurrenceMonths) == 0 {
		return errors.New(errors.Service, "recurrence months cannot be empty", errors.Validation, nil)
	}

	originalMonth := int(originalDate.Month())
	monthsSeen := make(map[int]bool)

	for _, month := range recurrenceMonths {
		// Check if month is valid (1-12)
		if month < 1 || month > 12 {
			return errors.New(errors.Service, "invalid month in recurrence array: must be between 1 and 12", errors.Validation, nil)
		}

		// Check for duplicates
		if monthsSeen[month] {
			return errors.New(errors.Service, "duplicate month in recurrence array", errors.Validation, nil)
		}
		monthsSeen[month] = true

		// Check if month is the same as original transaction month
		if month == originalMonth {
			return errors.New(errors.Service, "recurrence month cannot be the same as original transaction month", errors.Validation, nil)
		}
	}

	return nil
}

// calculateRecurrenceDate calculates the target date for a recurring transaction
func (s *service) calculateRecurrenceDate(originalDate time.Time, targetMonth int) time.Time {
	originalYear := originalDate.Year()
	originalMonth := int(originalDate.Month())
	day := originalDate.Day()

	// If target month is greater than original month, use same year
	// If target month is less than or equal to original month, use next year
	targetYear := originalYear
	if targetMonth <= originalMonth {
		targetYear = originalYear + 1
	}

	// Create the target date (Go automatically handles invalid dates like Feb 31 -> Mar 3)
	return time.Date(targetYear, time.Month(targetMonth), day,
		originalDate.Hour(), originalDate.Minute(), originalDate.Second(),
		originalDate.Nanosecond(), originalDate.Location())
}

// CreateDreamTransaction creates a transaction for a dream
func (s *service) CreateDreamTransaction(ctx context.Context, userID string, dreamID string, amount monetary.Amount) error {
	// Update streak information
	record, err := s.FindByUser(ctx, userID)
	if err != nil {
		return err
	}

	// Use UTC for consistent "day" calculation across timezones.
	// Or, if you store user-specific timezones, use that location.
	loc := time.UTC
	now := time.Now().In(loc)

	if record.Points.LastTransactionDate.IsZero() {
		// First-ever transaction for this user
		record.Points.Current = 1
		record.Points.Best = 1
	} else {
		// Truncate times to the beginning of the day (00:00:00) for accurate date comparison
		lastTxDay := time.Date(
			record.Points.LastTransactionDate.Year(),
			record.Points.LastTransactionDate.Month(),
			record.Points.LastTransactionDate.Day(),
			0, 0, 0, 0, loc)

		today := time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			0, 0, 0, 0, loc)

		// Calculate the difference in calendar days
		daysSinceLastTransaction := int(today.Sub(lastTxDay).Hours() / 24)

		if daysSinceLastTransaction == 0 {
			// Transaction on the same day. Do nothing to the streak.
			// The LastTransactionDate will be updated later.
		} else if daysSinceLastTransaction == 1 {
			// Consecutive day transaction: increment the streak
			record.Points.Current++
			if record.Points.Current > record.Points.Best {
				record.Points.Best = record.Points.Current
			}
		} else if daysSinceLastTransaction > 1 {
			// Streak is broken: reset to 1 for the current day's transaction
			record.Points.Current = 1
			record.Points.MissedDays = append(record.Points.MissedDays, record.Points.LastTransactionDate.AddDate(0, 0, 1))

			// EVALUATE LATER
			// Optional: More robust missed days calculation
			// This loop adds all days that were missed.
			// Note: This could create a large slice if the user was away for a long time.
			// You may want to cap it or stick with your simpler logic if it's sufficient.
			// missedDay := lastTxDay.AddDate(0, 0, 1)
			// for missedDay.Before(today) {
			// 	record.Points.MissedDays = append(record.Points.MissedDays, missedDay)
			// 	missedDay = missedDay.AddDate(0, 0, 1)
			// }
		}
	}
	// Always update the last transaction date to the current time
	record.Points.LastTransactionDate = now

	// Get the board, dream and contribution details from the board repository to avoid circular dependency
	dreamObjID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	board, err := s.DreamboardRepository.FindByDreamID(ctx, dreamObjID)
	if err != nil {
		return err
	}

	dream, err := s.DreamboardRepository.FindDream(ctx, board.ObjectID, dreamObjID)
	if err != nil {
		return err
	}

	// If the dream is personal update the dream's raised amount
	if !dream.IsShared {
		dream.CurrentRaisedAmount += amount
	}

	// If the dream is shared, update the dream's raised amount for all contributors
	if dream.IsShared {
		// Update dream's raised amount considering all contributors
		contributions, err := s.DreamboardRepository.FindContributionsByDreamID(ctx, dreamID)
		if err != nil {
			return err
		}

		var totalCurrentRaisedAmount monetary.Amount
		for _, contribution := range contributions {
			raisedAmount, err := s.getCurrentRaisedAmount(ctx, contribution.ContributorUserID, dreamID)
			if err != nil {
				return err
			}
			totalCurrentRaisedAmount += raisedAmount
		}

		// Sum the amounts of all active contributors
		dream.CurrentRaisedAmount = totalCurrentRaisedAmount
	}

	// Recalculate the duration in months if the monthly savings cost has changed
	if dream.MonthlySavings > 0 {
		duration := int((dream.EstimatedCost - dream.CurrentRaisedAmount) / dream.MonthlySavings)
		dream.CalculatedDurationMonths = &duration
	}
	if err := s.DreamboardRepository.UpdateDream(ctx, board.ObjectID, dream); err != nil {
		return err
	}

	if err := s.Repository.Update(ctx, record); err != nil {
		return err
	}

	// After successful financial record update, record investida for leagues
	if s.LeagueService != nil {
		if errLs := s.LeagueService.RecordTransactionForAllUserLeagues(ctx, userID, time.Now()); errLs != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to record investida for user %s in leagues: %v\n", userID, errLs) // Placeholder for proper logging
		}
	}

	// Check for Explorer achievement
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckExplorerAchievement(ctx, userID); err != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to check explorer achievement for user %s: %v\n", userID, err) // Placeholder for proper logging
		}
	}

	return nil
}

// FindTransaction finds a transaction in a user's financial record
func (s *service) FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error) {
	// Convert recordID to ObjectID
	recordObjID, err := primitive.ObjectIDFromHex(recordID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}

	// Get the financial record
	record, err := s.Repository.Find(ctx, recordObjID)
	if err != nil {
		return nil, err
	}

	// Attempt to convert transactionID to ObjectID for validation
	if _, err := primitive.ObjectIDFromHex(transactionID); err != nil {
		return nil, errors.New(errors.Service, "invalid transaction ID", errors.BadRequest, err)
	}

	// Search for the transaction in the record's transactions
	for year := range record.YearData {
		for month := range record.YearData[year] {
			monthData := record.YearData[year][month]
			for i := range monthData.Transactions {
				if monthData.Transactions[i].ObjectID.Hex() == transactionID {
					// Return a copy to avoid any pointer issues
					found := monthData.Transactions[i]
					return &found, nil
				}
			}
		}
	}

	return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
}

// FindAllTransactions retrieves all transactions for a user with optional category, month, and year filtering
func (s *service) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year, month int, planning bool) ([]*financialsheet.Transaction, error) {
	// Validate category type if provided
	if categoryType != "" {
		if err := categoryType.Validate(); err != nil {
			return nil, errors.New(errors.Service, "invalid category type", errors.Validation, err)
		}
	}

	allTransactions, err := s.Repository.FindAllTransactions(ctx, userID, categoryType, year, month)
	if err != nil {
		return nil, err
	}

	// If in planning mode and it is a cost of living instead of returning a transaction for each dream,
	// completely replace it with a transaction value from each dream monthly savings from the dreamboard
	if planning && categoryType == financialsheet.CategoryTypeCostsOfLiving {
		// Get the dreamboard
		dreamboard, err := s.DreamboardRepository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}

		// Get all transactions for the dreams category
		var dreamTransactions []*financialsheet.Transaction
		for _, transaction := range allTransactions {
			if transaction.Category == financialsheet.CategoryIdentifierDreams {
				dreamTransactions = append(dreamTransactions, transaction)
			}
		}

		// Remove all dream transactions
		for _, transaction := range dreamTransactions {
			for i, t := range allTransactions {
				if t.ObjectID == transaction.ObjectID {
					allTransactions = append(allTransactions[:i], allTransactions[i+1:]...)
					break
				}
			}
		}

		// Add a transaction for each dream if not completed
		for _, dream := range dreamboard.Dreams {
			if !dream.Completed {
				transaction := &financialsheet.Transaction{
					ObjectID:          primitive.NewObjectID(),
					Category:          financialsheet.CategoryIdentifierDreams,
					MoneySource:       financialsheet.MoneySourceOther,
					Value:             dream.MonthlySavings,
					Date:              dream.Deadline,
					AttachedDreamID:   dream.ID,
					AttachedDreamName: dream.Title,
					Type:              financialsheet.CategoryTypeCostsOfLiving,
				}
				allTransactions = append(allTransactions, transaction)
			}
		}
		return allTransactions, nil
	}

	return allTransactions, nil
}

// UpdateTransaction updates an existing transaction
func (s *service) UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	if record == nil {
		return nil, errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	if transaction == nil {
		return nil, errors.New(errors.Service, "invalid transaction", errors.BadRequest, nil)
	}

	// Validate transaction type matches category type
	category, err := s.FindCategoryByUserAndIdentifier(ctx, record.UserID, transaction.Category)
	if err != nil {
		return nil, err
	}
	if category.Type != transaction.Type {
		return nil, errors.New(errors.Service, "transaction type does not match category type", errors.Validation, nil)
	}

	year := transaction.Date.Year()
	month := transaction.Date.Format("01")

	// Initialize year data if it doesn't exist
	if record.YearData == nil {
		record.YearData = make(map[int]financialsheet.YearData)
	}
	if _, ok := record.YearData[year]; !ok {
		record.YearData[year] = make(financialsheet.YearData)
	}

	var found bool
	var oldTransaction *financialsheet.Transaction

	// Find existing transaction and get its old value
	for searchYear := range record.YearData {
		for searchMonth := range record.YearData[searchYear] {
			for i, t := range record.YearData[searchYear][searchMonth].Transactions {
				if t.ObjectID.Hex() == transaction.ID {
					oldTransaction = &record.YearData[searchYear][searchMonth].Transactions[i]
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	// Get month data for the target month (where we want to update the transaction)
	monthData := record.YearData[year][month]
	var updatedCategories []financialsheet.CategoryCard
	var updatedTransactions []financialsheet.Transaction

	// Update transactions
	for _, t := range monthData.Transactions {
		if t.ObjectID.Hex() == transaction.ID {
			updatedTransactions = append(updatedTransactions, *transaction)
		} else {
			updatedTransactions = append(updatedTransactions, t)
		}
	}

	// Update category summaries
	categoryMap := make(map[string]financialsheet.CategoryCard)
	for _, card := range monthData.Categories {
		categoryMap[card.Identifier] = card
	}

	// Remove old transaction value from old category
	if oldCard, exists := categoryMap[string(oldTransaction.Category)]; exists {
		oldCard.Value -= oldTransaction.Value
		categoryMap[string(oldTransaction.Category)] = oldCard
	}

	// Add new transaction value to new category
	if newCard, exists := categoryMap[string(transaction.Category)]; exists {
		newCard.Value += transaction.Value
		categoryMap[string(transaction.Category)] = newCard
	} else {
		categoryMap[string(transaction.Category)] = financialsheet.CategoryCard{
			ID:         category.ID,
			Identifier: string(transaction.Category),
			Name:       category.Name,
			Value:      transaction.Value,
		}
	}

	// Convert map back to slice
	for _, card := range categoryMap {
		if card.Value != 0 {
			updatedCategories = append(updatedCategories, card)
		}
	}

	// Create new month data with updated categories and transactions
	newMonthData := financialsheet.MonthData{
		Categories:   updatedCategories,
		Transactions: updatedTransactions,
	}
	record.YearData[year][month] = newMonthData

	// Recalculate totals
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByUserAndIdentifier(ctx, record.UserID, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	return record, nil
}

// DeleteTransaction removes a transaction from the record
func (s *service) DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	if record == nil {
		return nil, errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	var found bool
	var foundTransaction *financialsheet.Transaction

	// Find the transaction to remove
	for year := range record.YearData {
		for month := range record.YearData[year] {
			monthData := record.YearData[year][month]
			for i, transaction := range monthData.Transactions {
				if transaction.ObjectID.Hex() == transactionID {
					foundTransaction = &monthData.Transactions[i]

					// Update category summary
					var updatedCategories []financialsheet.CategoryCard
					for _, card := range monthData.Categories {
						newCard := card
						if card.Identifier == string(foundTransaction.Category) {
							// Subtract the transaction value from category summary
							newCard.Value -= foundTransaction.Value
						}
						updatedCategories = append(updatedCategories, newCard)
					}

					// Create new transactions slice without the removed transaction
					updatedTransactions := append(
						monthData.Transactions[:i],
						monthData.Transactions[i+1:]...,
					)

					// Update the month data
					record.YearData[year][month] = financialsheet.MonthData{
						Categories:   updatedCategories,
						Transactions: updatedTransactions,
					}

					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	if !found || foundTransaction == nil {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	// Recalculate totals from all transactions
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByUserAndIdentifier(ctx, record.UserID, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	return record, nil
}

// League System Methods - Implementations removed as they are now part of league.Service

// Utility
func (s *service) Initialize(ctx context.Context, userID string, userName string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		// Only proceed if the error is NotFound, otherwise return the error
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			return err
		}
		// For NotFound errors, we continue to create a new record
	} else if existing != nil {
		// If we found an existing record, return Conflict
		return errors.New(errors.Service, "financial record already exists", errors.Conflict, nil)
	}

	// currentTime := time.Now() // Unused variable
	newRecord := &financialsheet.Record{
		UserID:   userID,
		UserName: userName,
		Balance:  monetary.Amount(0),
		YearData: make(map[int]financialsheet.YearData),
		// League field removed from financialsheet.Record model
	}

	_, err = s.Repository.Create(ctx, newRecord)
	return err
}

// NoTransactions is a utility function to give a sequence to the user by register no transactions
func (s *service) NoTransactions(ctx context.Context, record *financialsheet.Record) error {
	if record == nil {
		return errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Validate abuse prevention
	if err := s.validateNoTransactionRequest(record, today); err != nil {
		return err
	}

	// Update streak information
	if record.Points.LastTransactionDate.IsZero() {
		record.Points.Current = 1
		record.Points.Best = record.Points.Current
	} else {
		// Normalize the last transaction date for comparison
		lastTransactionDay := time.Date(
			record.Points.LastTransactionDate.Year(),
			record.Points.LastTransactionDate.Month(),
			record.Points.LastTransactionDate.Day(),
			0, 0, 0, 0,
			record.Points.LastTransactionDate.Location(),
		)

		daysSinceLastTransaction := int(today.Sub(lastTransactionDay).Hours() / 24)

		if daysSinceLastTransaction == 1 {
			record.Points.Current++
			if record.Points.Current > record.Points.Best {
				record.Points.Best = record.Points.Current
			}
		} else if daysSinceLastTransaction > 1 {
			// Reset streak if more than one day has passed
			record.Points.Current = 1
			record.Points.MissedDays = append(record.Points.MissedDays, record.Points.LastTransactionDate.AddDate(0, 0, 1))
		}
		// Note: daysSinceLastTransaction == 0 (same day) should be prevented by validation
	}

	// Update tracking fields
	record.Points.LastTransactionDate = today
	record.Points.LastNoTransactionDate = today
	record.Points.NoTransactionDates = append(record.Points.NoTransactionDates, today)

	// Update record in database
	if err := s.Repository.Update(ctx, record); err != nil {
		return err
	}

	// After successful financial record update, record investida for leagues
	if s.LeagueService != nil {
		if errLs := s.LeagueService.RecordTransactionForAllUserLeagues(ctx, record.UserID, today); errLs != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to record investida for user %s in leagues: %v\n", record.UserID, errLs) // Placeholder for proper logging
		}
	}

	// Check for Explorer achievement
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckExplorerAchievement(ctx, record.UserID); err != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to check explorer achievement for user %s: %v\n", record.UserID, err) // Placeholder for proper logging
		}
	}

	return nil
}

// validateNoTransactionRequest validates if a NoTransaction request is allowed
func (s *service) validateNoTransactionRequest(record *financialsheet.Record, requestDate time.Time) error {
	// Check if user already marked today as no transactions
	if !record.Points.LastNoTransactionDate.IsZero() {
		lastNoTransactionDay := time.Date(
			record.Points.LastNoTransactionDate.Year(),
			record.Points.LastNoTransactionDate.Month(),
			record.Points.LastNoTransactionDate.Day(),
			0, 0, 0, 0,
			record.Points.LastNoTransactionDate.Location(),
		)

		if lastNoTransactionDay.Equal(requestDate) {
			return errors.New(errors.Service, "no transactions already marked for today", errors.Conflict, nil)
		}
	}

	// Check if the date is in the list of already marked no-transaction dates
	for _, noTransactionDate := range record.Points.NoTransactionDates {
		noTransactionDay := time.Date(
			noTransactionDate.Year(),
			noTransactionDate.Month(),
			noTransactionDate.Day(),
			0, 0, 0, 0,
			noTransactionDate.Location(),
		)

		if noTransactionDay.Equal(requestDate) {
			return errors.New(errors.Service, "no transactions already marked for this date", errors.Conflict, nil)
		}
	}

	// Check if there was already a regular transaction on the same day
	if !record.Points.LastTransactionDate.IsZero() {
		lastTransactionDay := time.Date(
			record.Points.LastTransactionDate.Year(),
			record.Points.LastTransactionDate.Month(),
			record.Points.LastTransactionDate.Day(),
			0, 0, 0, 0,
			record.Points.LastTransactionDate.Location(),
		)

		if lastTransactionDay.Equal(requestDate) {
			return errors.New(errors.Service, "cannot mark no transactions on the same day as a regular transaction", errors.Conflict, nil)
		}

		// Prevent retroactive marking beyond yesterday
		yesterday := requestDate.AddDate(0, 0, -1)
		if requestDate.Before(lastTransactionDay) && !requestDate.Equal(yesterday) {
			return errors.New(errors.Service, "cannot mark no transactions for dates before yesterday", errors.BadRequest, nil)
		}
	}

	return nil
}

// Helper
func (s *service) getCurrentRaisedAmount(ctx context.Context, userID string, dreamID string) (monetary.Amount, error) {
	// Get all transactions for the dream
	transactions, err := s.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return 0, err
	}

	// Filter only dreams transactions for a specific dream
	var dreamsTransactions []*financialsheet.Transaction
	for _, transaction := range transactions {
		if transaction.Category == financialsheet.CategoryIdentifierDreams && transaction.AttachedDreamID == dreamID {
			dreamsTransactions = append(dreamsTransactions, transaction)
		}
	}

	var currentMonthPaidAmount monetary.Amount
	for _, dreamTransaction := range dreamsTransactions {
		currentMonthPaidAmount += dreamTransaction.Value
	}

	return currentMonthPaidAmount, nil
}
