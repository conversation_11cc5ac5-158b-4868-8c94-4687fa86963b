package dashboard

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/golang-jwt/jwt"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// Mock service
type MockDashboardService struct {
	mock.Mock
}

func (m *MockDashboardService) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialMap), args.Error(1)
}

func (m *MockDashboardService) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount) (*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID, name, monthlyAmount)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardService) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardService) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	args := m.Called(ctx, id, name, monthlyAmount)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteIncomeSource(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardService) FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.StrategicFund), args.Error(1)
}

func (m *MockDashboardService) UpdateStrategicFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	args := m.Called(ctx, userID, currentValue)
	return args.Error(0)
}

func (m *MockDashboardService) UpdateStrategicFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	args := m.Called(ctx, userID, goalValue)
	return args.Error(0)
}

func (m *MockDashboardService) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	args := m.Called(ctx, userID, name, currentValue)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardService) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardService) UpdateInvestment(ctx context.Context, userID string, id string, name string, currentValue monetary.Amount) error {
	args := m.Called(ctx, userID, id, name, currentValue)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteInvestment(ctx context.Context, userID string, id string) error {
	args := m.Called(ctx, userID, id)
	return args.Error(0)
}

func (m *MockDashboardService) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	args := m.Called(ctx, userID, description, value)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardService) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardService) UpdateAsset(ctx context.Context, userID string, id string, description string, value monetary.Amount) error {
	args := m.Called(ctx, userID, id, description, value)
	return args.Error(0)
}

func (m *MockDashboardService) DeleteAsset(ctx context.Context, userID string, id string) error {
	args := m.Called(ctx, userID, id)
	return args.Error(0)
}

func (m *MockDashboardService) CreateMonthlySnapshot(ctx context.Context, userID string, snapshotDate time.Time, creationDate time.Time) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockDashboardService) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

func (m *MockDashboardService) FindLatestNetWorthSnapshot(ctx context.Context) (time.Time, error) {
	args := m.Called(ctx)
	return args.Get(0).(time.Time), args.Error(1)
}

// Financial Stress operations
func (m *MockDashboardService) FindFinancialStress(ctx context.Context, userID string, period string) (*dashboard.FinancialStress, error) {
	args := m.Called(ctx, userID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialStress), args.Error(1)
}

func (m *MockDashboardService) FindFixedExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	args := m.Called(ctx, userID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.ExpenseCategory), args.Error(1)
}

func (m *MockDashboardService) FindVariableExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	args := m.Called(ctx, userID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.ExpenseCategory), args.Error(1)
}

func (m *MockDashboardService) FindDebtExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	args := m.Called(ctx, userID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.ExpenseCategory), args.Error(1)
}

func (m *MockDashboardService) FindExpenseAnalysisDetails(ctx context.Context, userID string, categoryID string, period string) (*dashboard.CategoryBreakdown, error) {
	args := m.Called(ctx, userID, categoryID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.CategoryBreakdown), args.Error(1)
}

func (m *MockDashboardService) FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialIndependence), args.Error(1)
}

func (m *MockDashboardService) UpdateRetirementTargetAmount(ctx context.Context, userID string, targetAmount monetary.Amount) error {
	args := m.Called(ctx, userID, targetAmount)
	return args.Error(0)
}

func (m *MockDashboardService) GetPortugueseMonthName(month time.Month) string {
	args := m.Called(month)
	return args.String(0)
}

// Test suite
type DashboardControllerTestSuite struct {
	suite.Suite
	controller  Controller
	mockService *MockDashboardService
	echo        *echo.Echo
	testUserID  string
}

func (suite *DashboardControllerTestSuite) SetupTest() {
	suite.mockService = new(MockDashboardService)
	suite.controller = New(suite.mockService, nil)
	suite.echo = echo.New()
	suite.testUserID = "test-user-123"
	os.Setenv("API_ACCESS_JWT_KEY", "test-secret")
}

func TestDashboardControllerTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardControllerTestSuite))
}

func (suite *DashboardControllerTestSuite) generateTestToken() string {
	claims := &jwt.MapClaims{
		"uid":  suite.testUserID,
		"role": "user",
		"exp":  time.Now().Add(time.Hour * 1).Unix(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, _ := token.SignedString([]byte(os.Getenv("API_ACCESS_JWT_KEY")))
	return tokenString
}

func (suite *DashboardControllerTestSuite) createRequestWithAuth(method, url string, body interface{}) (*http.Request, *httptest.ResponseRecorder) {
	var reqBody []byte
	if body != nil {
		reqBody, _ = json.Marshal(body)
	}

	req := httptest.NewRequest(method, url, bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

	// Add mock JWT token
	tokenString := suite.generateTestToken()
	req.Header.Set("Authorization", "Bearer "+tokenString)

	rec := httptest.NewRecorder()
	return req, rec
}

func (suite *DashboardControllerTestSuite) TestFindFinancialMap() {
	// Mock data
	financialMap := &dashboard.FinancialMap{
		UserID:           suite.testUserID,
		MonthlyIncome:    5000,
		TotalInvestments: 25000,
		TotalAssets:      80000,
	}

	suite.mockService.On("FindFinancialMap", mock.Anything, suite.testUserID).Return(financialMap, nil)

	req, rec := suite.createRequestWithAuth("GET", "/dashboard/financialmaps/me", nil)
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.FindFinancialMap()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response dashboard.FinancialMap
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(financialMap.UserID, response.UserID)
	suite.Equal(financialMap.MonthlyIncome, response.MonthlyIncome)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestCreateIncomeSource() {
	request := CreateIncomeSourceRequest{
		Name:          "Test Salary",
		MonthlyAmount: 5000,
	}

	expectedIncomeSource := &dashboard.IncomeSource{
		UserID:        suite.testUserID,
		Name:          request.Name,
		MonthlyAmount: request.MonthlyAmount,
	}

	suite.mockService.On("CreateIncomeSource", mock.Anything, suite.testUserID, request.Name, request.MonthlyAmount).Return(expectedIncomeSource, nil)

	req, rec := suite.createRequestWithAuth("POST", "/dashboard/financialmaps/me/incomes", request)
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.CreateIncomeSource()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusCreated, rec.Code)

	var response dashboard.IncomeSource
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(expectedIncomeSource.Name, response.Name)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestUpdateStrategicFund() {
	request := UpdateStrategicFundRequest{
		CurrentValue: 15000,
	}

	suite.mockService.On("UpdateStrategicFund", mock.Anything, suite.testUserID, request.CurrentValue).Return(nil)

	req, rec := suite.createRequestWithAuth("PUT", "/dashboard/financialmaps/me/strategicfunds", request)
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.UpdateStrategicFund()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusNoContent, rec.Code)

	suite.mockService.AssertExpectations(suite.T())
}

func (suite *DashboardControllerTestSuite) TestCreateInvestment() {
	request := CreateInvestmentRequest{
		Name:         "Tesouro Selic",
		CurrentValue: 25000,
	}

	expectedInvestment := &dashboard.Investment{
		UserID:       suite.testUserID,
		Name:         request.Name,
		CurrentValue: request.CurrentValue,
	}

	suite.mockService.On("CreateInvestment", mock.Anything, suite.testUserID, request.Name, request.CurrentValue).Return(expectedInvestment, nil)

	req, rec := suite.createRequestWithAuth("POST", "/dashboard/financialmaps/me/investments", request)
	c := suite.echo.NewContext(req, rec)

	handler := suite.controller.CreateInvestment()
	err := handler(c)

	suite.Require().NoError(err)
	suite.Equal(http.StatusCreated, rec.Code)

	var response dashboard.Investment
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.Require().NoError(err)
	suite.Equal(expectedInvestment.Name, response.Name)

	suite.mockService.AssertExpectations(suite.T())
}

func TestTokenExtraction(t *testing.T) {
	// This is a simple test to verify token extraction logic
	// In a real scenario, you would test the actual middleware
	mockToken := &token.Details{
		Uid: "test-user-123",
	}

	assert.Equal(t, "test-user-123", mockToken.Uid)
}
